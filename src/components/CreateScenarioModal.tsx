import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Package, Target, Plus, RefreshCw } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { FlowDiagramResponse } from '@/services/flowDiagramApi';

interface SavedFlow {
  title: string;
  nodes: any[];
  edges: any[];
  flowType?: 'inventory' | 'scenario';
}

interface CreateScenarioModalProps {
  open: boolean;
  onClose: () => void;
  onCreateScenario: (data: any) => void;
  savedFlows: SavedFlow[];
  flowDiagrams?: FlowDiagramResponse[]; // Add flow diagrams from API
  onRefreshFlowDiagrams?: () => void; // Add refresh function
}

const periodLengthOptions = [3, 6, 9, 12, 15, 24, 48];

export const CreateScenarioModal: React.FC<CreateScenarioModalProps> = ({
  open,
  onClose,
  onCreateScenario,
  savedFlows,
  flowDiagrams = [],
  onRefreshFlowDiagrams
}) => {
  // Debug logging
  React.useEffect(() => {
    if (open) {
      console.log('CreateScenarioModal opened with data:');
      console.log('flowDiagrams:', flowDiagrams);
      console.log('flowDiagrams length:', flowDiagrams?.length);
      console.log('savedFlows:', savedFlows);
      console.log('savedFlows length:', savedFlows?.length);
      console.log('Should show Create from scratch: YES (always)');
      console.log('Should show API diagrams:', !!(flowDiagrams && flowDiagrams.length > 0));
      console.log('Should show saved flows:', !!(savedFlows && savedFlows.length > 0));

      // Debug individual flow diagrams
      if (flowDiagrams && flowDiagrams.length > 0) {
        flowDiagrams.forEach((diagram, index) => {
          console.log(`Flow diagram ${index} - Full object:`, diagram);
          console.log(`Flow diagram ${index} - Object keys:`, Object.keys(diagram || {}));
          console.log(`Flow diagram ${index} - Extracted fields:`, {
            uuid: diagram?.uuid,
            name: diagram?.name,
            tag: diagram?.tag,
            created_at: diagram?.created_at
          });
        });
      }
    }
  }, [open, flowDiagrams, savedFlows]);

  const [baseScenario, setBaseScenario] = useState<string>('');
  const [scenarioName, setScenarioName] = useState<string>('');
  const [timePeriodType, setTimePeriodType] = useState<'single' | 'multiple'>('single');

  // Debug baseScenario changes
  React.useEffect(() => {
    console.log('baseScenario changed to:', baseScenario);
  }, [baseScenario]);
  
  // Single time period states
  const [singlePeriodLength, setSinglePeriodLength] = useState<number>(3);
  const [singleStartDate, setSingleStartDate] = useState<Date | undefined>(undefined);
  
  // Multiple time periods states
  const [multiplePeriodLength, setMultiplePeriodLength] = useState<number>(3);
  const [multipleNumberOfPeriods, setMultipleNumberOfPeriods] = useState<number>(1);
  const [multipleStartDate, setMultipleStartDate] = useState<Date | undefined>(undefined);

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    console.log('Validating form with baseScenario:', baseScenario);
    console.log('Validating form with scenarioName:', scenarioName);

    if (!baseScenario || baseScenario.trim() === '') {
      newErrors.baseScenario = 'Please select a base scenario or create from scratch';
    }

    if (!scenarioName.trim()) {
      newErrors.scenarioName = 'Scenario name is required';
    }

    if (timePeriodType === 'single') {
      if (!singleStartDate) {
        newErrors.singleStartDate = 'Start date is required';
      }
    } else {
      if (!multipleStartDate) {
        newErrors.multipleStartDate = 'Start date is required';
      }
      if (multipleNumberOfPeriods < 1) {
        newErrors.multipleNumberOfPeriods = 'Number of periods must be at least 1';
      }
    }

    console.log('Validation errors:', newErrors);
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    console.log('handleSubmit called');
    console.log('Current form state:', {
      baseScenario,
      scenarioName,
      timePeriodType,
      singleStartDate,
      multipleStartDate
    });

    if (!validateForm()) {
      console.log('Form validation failed');
      return;
    }

    console.log('Form validation passed, creating scenario data');

    const scenarioData = {
      baseScenario,
      scenarioName: scenarioName.trim(),
      timePeriodType,
      timePeriod: timePeriodType === 'single'
        ? {
            length: singlePeriodLength,
            startDate: singleStartDate
          }
        : {
            length: multiplePeriodLength,
            numberOfPeriods: multipleNumberOfPeriods,
            startDate: multipleStartDate
          },
      // API-compatible fields
      period_type: timePeriodType === 'single' ? 'single' : 'multiple',
      period_length: timePeriodType === 'single' ? singlePeriodLength : multiplePeriodLength,
      number_of_periods: timePeriodType === 'single' ? 1 : multipleNumberOfPeriods,
      start_date: (timePeriodType === 'single' ? singleStartDate : multipleStartDate)?.toISOString().split('T')[0],
      base_scenario_id: baseScenario === 'scratch' ? null : baseScenario
    };

    console.log('Scenario data created:', scenarioData);
    onCreateScenario(scenarioData);
    handleClose();
  };

  const handleClose = () => {
    // Reset form
    setBaseScenario('');
    setScenarioName('');
    setTimePeriodType('single');
    setSinglePeriodLength(3);
    setSingleStartDate(undefined);
    setMultiplePeriodLength(3);
    setMultipleNumberOfPeriods(1);
    setMultipleStartDate(undefined);
    setErrors({});
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Scenario</DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-6 py-4">

          {/* Base Scenario Selection */}
          <div className="grid gap-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="baseScenario">Base Scenario *</Label>
              {onRefreshFlowDiagrams && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={onRefreshFlowDiagrams}
                  className="h-6 px-2"
                >
                  <RefreshCw size={12} />
                </Button>
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Select an existing flow diagram to use as a starting point, or create from scratch
            </p>
            <Select
              value={baseScenario}
              onValueChange={(value) => {
                setBaseScenario(value);
                // Clear the base scenario error when a selection is made
                if (errors.baseScenario) {
                  setErrors(prev => ({ ...prev, baseScenario: '' }));
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a base scenario or create from scratch">
                  {baseScenario ? (
                    baseScenario === 'scratch' ? 'Create from scratch' :
                    flowDiagrams?.find((d, index) => (d.uuid || `api-flow-${index}`) === baseScenario)?.name ||
                    savedFlows?.find(f => f.title === baseScenario)?.title ||
                    baseScenario
                  ) : 'Select a base scenario or create from scratch'}
                </SelectValue>
              </SelectTrigger>
              <SelectContent className="max-h-[300px]">
                {/* Always show "Create from scratch" option first */}
                <SelectItem value="scratch">
                  <div className="flex items-center gap-2">
                    <Plus size={14} />
                    <span>Create from scratch</span>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground ml-auto">
                      New
                    </div>
                  </div>
                </SelectItem>

                {/* Show separator if there are other options */}
                {((flowDiagrams && flowDiagrams.length > 0) || (savedFlows && savedFlows.length > 0)) && (
                  <div className="border-t my-1" />
                )}

                {/* Debug info - show if no valid options are available */}
                {(!flowDiagrams || flowDiagrams.length === 0) && (!savedFlows || savedFlows.length === 0) && (
                  <div className="px-3 py-2 text-xs text-muted-foreground">
                    No saved inventories or scenarios available
                  </div>
                )}

                {/* Show flow diagrams from API */}
                {flowDiagrams && flowDiagrams.length > 0 && (
                  <>
                    {flowDiagrams.map((flowDiagram, index) => {
                      // Ensure we have valid data before rendering
                      if (!flowDiagram) {
                        console.log(`Flow diagram ${index} is null/undefined`);
                        return null;
                      }

                      // Check for uuid or use index as fallback
                      const flowId = flowDiagram.uuid || `api-flow-${index}`;
                      console.log(`Flow diagram ${index} - ID: ${flowId}, Name: ${flowDiagram.name}, Tag: ${flowDiagram.tag}`);

                      return (
                        <SelectItem key={flowId} value={flowId}>
                          <div className="flex items-center gap-2">
                            {flowDiagram.tag && flowDiagram.tag.startsWith('SCENARIO') ? (
                              <Target size={14} />
                            ) : (
                              <Package size={14} />
                            )}
                            <span>{flowDiagram.name || 'Unnamed Flow'}</span>
                            <div className="flex items-center gap-1 text-xs text-muted-foreground ml-auto">
                              {flowDiagram.tag && flowDiagram.tag.startsWith('SCENARIO') ? 'Scenario' : 'Inventory'}
                            </div>
                          </div>
                        </SelectItem>
                      );
                    })}
                  </>
                )}

                {/* Show saved flows if no API data or as additional options */}
                {savedFlows && savedFlows.length > 0 && (
                  <>
                    {flowDiagrams && flowDiagrams.length > 0 && (
                      <div className="border-t my-1" />
                    )}
                    {savedFlows.map((flow, index) => {
                      // Ensure we have valid data before rendering
                      if (!flow || !flow.title) {
                        return null;
                      }

                      return (
                        <SelectItem key={`flow-${index}`} value={flow.title}>
                          <div className="flex items-center gap-2">
                            {flow.flowType === 'scenario' ? (
                              <Target size={14} />
                            ) : (
                              <Package size={14} />
                            )}
                            <span>{flow.title}</span>
                            <div className="flex items-center gap-1 text-xs text-muted-foreground ml-auto">
                              {flow.flowType === 'scenario' ? 'Scenario' : 'Inventory'} (Local)
                            </div>
                          </div>
                        </SelectItem>
                      );
                    })}
                  </>
                )}
              </SelectContent>
            </Select>
            {errors.baseScenario && (
              <span className="text-sm text-red-500">{errors.baseScenario}</span>
            )}
          </div>

          {/* Scenario Name */}
          <div className="grid gap-2">
            <Label htmlFor="scenarioName">Scenario Name *</Label>
            <Input
              id="scenarioName"
              value={scenarioName}
              onChange={(e) => {
                setScenarioName(e.target.value);
                // Clear the scenario name error when user starts typing
                if (errors.scenarioName) {
                  setErrors(prev => ({ ...prev, scenarioName: '' }));
                }
              }}
              placeholder="Enter scenario name"
            />
            {errors.scenarioName && (
              <span className="text-sm text-red-500">{errors.scenarioName}</span>
            )}
          </div>

          {/* Time Period Selection */}
          <div className="grid gap-3">
            <Label>Time Period Type *</Label>
            <RadioGroup
              value={timePeriodType}
              onValueChange={(value: 'single' | 'multiple') => setTimePeriodType(value)}
              className="flex gap-6"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="single" id="single" />
                <Label htmlFor="single">Single Time Period</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="multiple" id="multiple" />
                <Label htmlFor="multiple">Multiple Time Periods</Label>
              </div>
            </RadioGroup>
          </div>

          {/* Single Time Period */}
          {timePeriodType === 'single' && (
            <div className="grid gap-4 p-4 border rounded-lg">
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label>Length of Period (months)</Label>
                  <Select
                    value={singlePeriodLength.toString()}
                    onValueChange={(value) => setSinglePeriodLength(parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {periodLengthOptions.map(option => (
                        <SelectItem key={option} value={option.toString()}>
                          {option} months
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid gap-2">
                  <Label>Start Year and Month</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "justify-start text-left font-normal",
                          !singleStartDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {singleStartDate ? (
                          format(singleStartDate, "MMMM yyyy")
                        ) : (
                          <span>Pick a date</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={singleStartDate}
                        onSelect={setSingleStartDate}
                        initialFocus
                        className="pointer-events-auto"
                      />
                    </PopoverContent>
                  </Popover>
                  {errors.singleStartDate && (
                    <span className="text-sm text-red-500">{errors.singleStartDate}</span>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Multiple Time Periods */}
          {timePeriodType === 'multiple' && (
            <div className="grid gap-4 p-4 border rounded-lg">
              <div className="grid gap-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label>Length of Period (months)</Label>
                    <Select
                      value={multiplePeriodLength.toString()}
                      onValueChange={(value) => setMultiplePeriodLength(parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {periodLengthOptions.map(option => (
                          <SelectItem key={option} value={option.toString()}>
                            {option} months
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid gap-2">
                    <Label>Number of Periods</Label>
                    <Input
                      type="number"
                      min="1"
                      max="45"
                      value={multipleNumberOfPeriods}
                      onChange={(e) => setMultipleNumberOfPeriods(parseInt(e.target.value) || 1)}
                    />
                    {errors.multipleNumberOfPeriods && (
                      <span className="text-sm text-red-500">{errors.multipleNumberOfPeriods}</span>
                    )}
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label>Start Year and Month</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "justify-start text-left font-normal",
                          !multipleStartDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {multipleStartDate ? (
                          format(multipleStartDate, "MMMM yyyy")
                        ) : (
                          <span>Pick a date</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={multipleStartDate}
                        onSelect={setMultipleStartDate}
                        initialFocus
                        className="pointer-events-auto"
                      />
                    </PopoverContent>
                  </Popover>
                  {errors.multipleStartDate && (
                    <span className="text-sm text-red-500">{errors.multipleStartDate}</span>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>
            Create Scenario
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
